import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import prisma from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Not authenticated.' }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: {
        emailVerified: true,
      },
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found.' }, { status: 404 });
    }

    return NextResponse.json({ 
      isVerified: !!user.emailVerified,
      emailVerified: user.emailVerified 
    }, { status: 200 });

  } catch (error) {
    console.error('Verification status check error:', error);
    return NextResponse.json({ error: 'An error occurred while checking verification status.' }, { status: 500 });
  }
}
